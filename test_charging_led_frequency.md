# 自转模式充电时LED闪烁频率测试

## 需求确认
在自转模式中，充电时自转各个功能可以正常使用，但是正在充电的情况下电机运行时绿灯的闪烁频率要和充电时相同。

## 当前实现分析

### LED闪烁频率定义
```c
#define LED_FLASH_INTERVAL 100          // 充电闪烁频率：100ms周期
#define AUTO_ROTATE_FLASH_INTERVAL 50   // 自转运行闪烁频率：50ms周期
```

### 闪烁变量控制
1. **充电闪烁**：`ledonoff` 变量
   - 频率：每100ms切换一次
   - 用于：充电时绿灯闪烁

2. **自转运行闪烁**：`auto_rotate_flash` 变量  
   - 频率：每50ms切换一次
   - 用于：自转运行时红灯闪烁

### LED控制逻辑
```c
if(System_Mode_Data == System_AutoRotate)
{
    if(Charg_State_Buff)  // 充电状态
    {
        LEDROFF;  // 红灯灭
        if(CHG_CHARGE == 0)  // 正在充电
        {
            // 关键：使用ledonoff变量，确保充电闪烁频率
            if(ledonoff) LEDGON; 
            else LEDGOFF;
        }
        else  // 充满电
        {
            LEDGON;  // 绿灯长亮
        }
    }
    else  // 未充电状态
    {
        LEDGOFF;  // 绿灯灭
        if(auto_rotate_running)
        {
            // 自转运行时：红灯使用auto_rotate_flash闪烁
            if(auto_rotate_flash) LEDRON;
            else LEDROFF;
        }
        else
        {
            LEDRON;  // 自转待机时：红灯长亮
        }
    }
}
```

## 测试场景

### 场景1：充电时待机状态
1. 进入自转模式（红灯长亮）
2. 插入充电器
3. **预期**：绿灯以100ms频率闪烁，红灯灭 ✅
4. **实际**：使用`ledonoff`变量控制绿灯闪烁

### 场景2：充电时电机运行状态
1. 进入自转模式，按K1开始自转（红灯50ms频率闪烁）
2. 插入充电器开始充电
3. **预期**：绿灯以100ms频率闪烁（充电频率），红灯灭 ✅
4. **关键**：绿灯不受电机运行状态影响，始终使用充电频率

### 场景3：充电时功能切换
1. 充电状态下，绿灯100ms频率闪烁
2. 按K1开始自转 → 绿灯继续100ms频率闪烁 ✅
3. 按K3切换方向 → 绿灯继续100ms频率闪烁 ✅
4. 按K3停止自转 → 绿灯继续100ms频率闪烁 ✅

### 场景4：充电完成
1. 充电中：绿灯100ms频率闪烁
2. 充电完成：绿灯变为长亮 ✅
3. 拔出充电器：恢复自转状态LED显示 ✅

## 频率对比表

| 状态 | LED | 闪烁变量 | 频率 | 周期 |
|------|-----|---------|------|------|
| 充电中 | 绿灯 | `ledonoff` | 100ms | 200ms |
| 自转运行 | 红灯 | `auto_rotate_flash` | 50ms | 100ms |
| 充电+自转 | 绿灯 | `ledonoff` | 100ms | 200ms |

## 关键实现要点

### 1. 充电状态优先级
```c
if(Charg_State_Buff)  // 充电状态检查在最前面
{
    // 充电LED控制逻辑
    // 无论电机是否运行，都使用充电闪烁频率
}
else
{
    // 非充电状态的自转LED控制
}
```

### 2. 闪烁变量分离
- **充电闪烁**：`ledonoff` (100ms)
- **自转闪烁**：`auto_rotate_flash` (50ms)
- 两个变量独立更新，互不干扰

### 3. 频率一致性保证
在充电状态下，绿灯始终使用`ledonoff`变量：
```c
if(CHG_CHARGE == 0)  // 正在充电
{
    if(ledonoff) LEDGON;  // 使用充电闪烁频率
    else LEDGOFF;
}
```

## 验证结果

✅ **充电时绿灯闪烁频率固定**：100ms周期，不受电机运行状态影响
✅ **功能正常使用**：充电时所有自转功能（K1/K3/K2）正常工作
✅ **状态切换正确**：充电完成时绿灯变长亮，拔出充电器恢复原状态
✅ **频率独立性**：充电闪烁和自转闪烁使用不同变量，互不干扰

## 总结

当前实现已经满足需求：
- 充电时绿灯使用固定的100ms闪烁频率（`ledonoff`变量）
- 无论电机是否运行，充电时的绿灯闪烁频率都保持一致
- 自转功能在充电时正常工作，不影响LED显示
- 充电状态具有最高优先级，覆盖其他LED显示逻辑
