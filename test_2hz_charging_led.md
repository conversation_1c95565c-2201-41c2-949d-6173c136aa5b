# 2Hz充电LED闪烁频率更新

## 修改内容

将充电闪烁频率从1Hz调整为2Hz。

### 修改详情

#### 1. 频率定义更新
```c
// 修改前
#define CHARGE_FLASH_INTERVAL 500  // 1Hz闪烁(1000ms周期，500ms切换一次)

// 修改后  
#define CHARGE_FLASH_INTERVAL 250  // 2Hz闪烁(500ms周期，250ms切换一次)
```

#### 2. 注释更新
- 所有相关注释从"1Hz"更新为"2Hz"
- 确保代码注释与实际实现一致

## 频率计算

### 2Hz闪烁参数
- **频率**: 2Hz（每秒闪烁2次）
- **完整周期**: 500ms
- **切换间隔**: 250ms
- **LED状态**: 250ms亮 → 250ms灭 → 250ms亮 → 250ms灭...

### 时序图
```
时间轴:    0ms   250ms  500ms  750ms  1000ms
charge_flash: 0  →  1   →   0   →   1   →   0
绿灯状态:   灭  →  亮   →   灭   →   亮   →   灭
周期:       |←─ 500ms ─→|←─ 500ms ─→|
频率:           2Hz           2Hz
```

## 频率对比表

| 状态 | LED | 变量 | 频率 | 周期 | 切换间隔 |
|------|-----|------|------|------|---------|
| 充电中 | 绿灯 | `charge_flash` | **2Hz** | **500ms** | **250ms** |
| 自转运行 | 红灯 | `auto_rotate_flash` | 10Hz | 100ms | 50ms |
| 其他闪烁 | - | `ledonoff` | 5Hz | 200ms | 100ms |

## 测试验证

### 场景1：自转模式充电
1. 进入自转模式
2. 插入充电器
3. **预期**: 绿灯以2Hz频率闪烁（250ms亮，250ms灭）
4. **验证**: 每秒看到2次完整的亮-灭循环

### 场景2：充电时电机运行
1. 充电状态下按K1开始自转
2. **预期**: 绿灯继续以2Hz频率闪烁，不受电机状态影响
3. **验证**: 绿灯闪烁频率保持一致

### 场景3：普通模式充电
1. 普通模式下插入充电器
2. **预期**: 绿灯以2Hz频率闪烁
3. **验证**: 与自转模式下充电频率一致

## 用户体验

### 2Hz vs 1Hz对比
- **1Hz**: 较慢，1秒亮1秒灭，可能感觉不够明显
- **2Hz**: 适中，0.25秒亮0.25秒灭，更容易察觉充电状态
- **优势**: 2Hz频率更容易被用户注意到，同时不会过于频繁

### 与其他LED频率的区分
- **充电闪烁**: 2Hz（中等频率）
- **自转运行**: 10Hz（快速闪烁）
- **其他状态**: 5Hz（较快闪烁）

不同频率帮助用户快速识别设备状态。

## 总结

✅ **频率更新完成**: 充电LED闪烁频率已从1Hz调整为2Hz
✅ **代码一致性**: 所有相关注释和定义都已更新
✅ **功能保持**: 充电LED控制逻辑保持不变，只是频率调整
✅ **用户体验**: 2Hz频率更容易被察觉，提供更好的充电状态指示

现在充电时绿灯将以2Hz频率闪烁，每250ms切换一次状态，提供清晰的充电状态指示。
