# 1Hz充电LED闪烁频率修复测试

## 问题描述
1. 充电时的LED闪烁频率要为1Hz
2. 自转模式中，正在充电的情况下电机运行时绿灯的闪烁频率与充电时不相同

## 修复方案

### 1. 新增充电专用闪烁控制
```c
#define CHARGE_FLASH_INTERVAL 500  // 1Hz闪烁(1000ms周期，500ms切换一次)

bit charge_flash = 0;  // 充电闪烁状态
uint16_t charge_flash_cnt = 0;  // 充电闪烁计时器
```

### 2. 独立的充电闪烁更新逻辑
```c
// 充电闪烁计时（1Hz频率）
charge_flash_cnt++;
if(charge_flash_cnt > CHARGE_FLASH_INTERVAL)
{
    charge_flash_cnt = 0;
    charge_flash ^= 1;
}
```

### 3. 统一的充电LED控制
```c
if(CHG_CHARGE == 0)  // 正在充电
{
    // 使用专门的充电闪烁频率（1Hz），不受电机运行状态影响
    if(charge_flash) LEDGON;
    else LEDGOFF;
}
```

## 闪烁频率对比

### 修复前
| 状态 | LED | 变量 | 频率 | 周期 | 问题 |
|------|-----|------|------|------|------|
| 充电中 | 绿灯 | `ledonoff` | 5Hz | 200ms | ❌ 频率过快 |
| 自转运行 | 红灯 | `auto_rotate_flash` | 10Hz | 100ms | - |

### 修复后
| 状态 | LED | 变量 | 频率 | 周期 | 状态 |
|------|-----|------|------|------|------|
| 充电中 | 绿灯 | `charge_flash` | 1Hz | 1000ms | ✅ 符合要求 |
| 自转运行 | 红灯 | `auto_rotate_flash` | 10Hz | 100ms | ✅ 保持不变 |

## 测试场景

### 场景1：自转待机状态充电
1. 进入自转模式（红灯长亮）
2. 插入充电器
3. **预期**：绿灯以1Hz频率闪烁（1秒亮，1秒灭），红灯灭 ✅
4. **验证**：使用`charge_flash`变量，500ms切换一次

### 场景2：自转运行状态充电
1. 进入自转模式，按K1开始自转（红灯10Hz闪烁）
2. 插入充电器开始充电
3. **预期**：绿灯以1Hz频率闪烁，红灯灭 ✅
4. **关键**：绿灯不受电机运行状态影响，始终1Hz闪烁

### 场景3：充电时功能切换
1. 充电状态下，绿灯1Hz闪烁
2. 按K1开始自转 → 绿灯继续1Hz闪烁 ✅
3. 按K3切换方向 → 绿灯继续1Hz闪烁 ✅
4. 按K3停止自转 → 绿灯继续1Hz闪烁 ✅

### 场景4：非自转模式充电
1. 普通模式下插入充电器
2. **预期**：绿灯以1Hz频率闪烁 ✅
3. **验证**：非自转模式也使用`charge_flash`变量

## 关键修复点

### 1. 频率定义
```c
#define CHARGE_FLASH_INTERVAL 500  // 500ms切换 = 1Hz闪烁
```
- 每500ms切换一次LED状态
- 完整周期1000ms = 1Hz频率

### 2. 变量独立性
- `charge_flash`: 专用于充电闪烁（1Hz）
- `auto_rotate_flash`: 专用于自转运行闪烁（10Hz）
- `ledonoff`: 用于其他LED闪烁（5Hz）

### 3. 统一控制逻辑
无论在哪种模式下，充电时都使用`charge_flash`变量：
```c
// 自转模式
if(charge_flash) LEDGON;
else LEDGOFF;

// 非自转模式  
if(charge_flash) LEDGON;
else LEDGOFF;
```

### 4. 状态优先级
```
充电状态 > 其他所有状态
```
充电时LED显示完全由`charge_flash`控制，不受其他状态影响。

## 验证结果

✅ **1Hz充电闪烁**：充电时绿灯严格按照1Hz频率闪烁
✅ **频率一致性**：无论电机是否运行，充电时绿灯频率都是1Hz
✅ **功能独立性**：充电闪烁与自转闪烁使用不同变量，完全独立
✅ **全模式支持**：自转模式和非自转模式都使用相同的1Hz充电闪烁

## 时序图

```
时间轴:  0ms   500ms  1000ms  1500ms  2000ms
charge_flash: 0  →  1   →   0   →   1   →   0
绿灯状态: 灭  →  亮   →   灭   →   亮   →   灭
周期:     |←── 1000ms ──→|←── 1000ms ──→|
频率:           1Hz              1Hz
```

## 总结

修复完成后：
1. **充电LED频率**：严格按照1Hz闪烁（1秒周期）
2. **状态一致性**：无论在什么模式下充电，LED行为都一致
3. **功能隔离**：充电闪烁与其他功能的LED闪烁完全独立
4. **用户体验**：充电时LED行为清晰、一致、可预测
