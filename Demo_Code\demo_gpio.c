/*******************************************************************************
* Copyright (C) 2019 China Micro Semiconductor Limited Company. All Rights Reserved.
*
* This software is owned and published by:
* CMS LLC, No 2609-10, Taurus Plaza, TaoyuanRoad, NanshanDistrict, Shenzhen, China.
*
* BY DOWNLOADING, INSTALLING OR USING THIS SOFTWARE, YOU AGREE TO BE BOUND
* BY ALL THE TERMS AND CONDITIONS OF THIS AGREEMENT.
*
* This software contains source code for use with CMS
* components. This software is licensed by CMS to be adapted only
* for use in systems utilizing CMS components. CMS shall not be
* responsible for misuse or illegal use of this software for devices not
* supported herein. CMS is providing this software "AS IS" and will
* not be responsible for issues arising from incorrect user implementation
* of the software.
*
* This software may be replicated in part or whole for the licensed use,
* with the restriction that this Disclaimer and Copyright notice must be
* included with each copy of this software, whether used in part or whole,
* at all times.
*/

/****************************************************************************/
/** \file demo_gpio.c
**
**  
**
**	History:
**	
*****************************************************************************/
/****************************************************************************/
/*	include files
*****************************************************************************/
#include "demo_gpio.h"

/****************************************************************************/
/*	Local pre-processor symbols('#define')
****************************************************************************/

/****************************************************************************/
/*	Global variable definitions(declared in header file with 'extern')
****************************************************************************/

/****************************************************************************/
/*	Local type definitions('typedef')
****************************************************************************/

/****************************************************************************/
/*	Local variable  definitions('static')
****************************************************************************/

/****************************************************************************/
/*	Local function prototypes('static')
****************************************************************************/

/****************************************************************************/
/*	Function implementation - global ('extern') and local('static')
****************************************************************************/

/******************************************************************************
 ** \brief	 GPIO_Config
 ** \param [in] none
 **          GPIO中断功能
 ** \return  none
 ** \note  
 ******************************************************************************/
void GPIO_Config(void)
{
	/*
	(1)设置P23 IO功能
	*/
////	GPIO_SET_MUX_MODE(P23CFG, GPIO_MUX_GPIO);		//设置P23为GPIO模式
////	GPIO_ENABLE_INPUT(P2TRIS, GPIO_PIN_3);			//设置为输入模式
////	GPIO_ENABLE_RD(P2RD, GPIO_PIN_3);				//开启下拉
	
	//A_Phase
	GPIO_SET_MUX_MODE(P24CFG, GPIO_MUX_GPIO);
	GPIO_ENABLE_OUTPUT(P2TRIS, GPIO_PIN_4);
	A_Phase = Off;
	
	//B_Phase
	GPIO_SET_MUX_MODE(P25CFG, GPIO_MUX_GPIO);
	GPIO_ENABLE_OUTPUT(P2TRIS, GPIO_PIN_5);
	B_Phase = Off;

	//C_Phase
	GPIO_SET_MUX_MODE(P26CFG, GPIO_MUX_GPIO);
	GPIO_ENABLE_OUTPUT(P2TRIS, GPIO_PIN_6);
	C_Phase = Off;
	
	//D_Phase
	GPIO_SET_MUX_MODE(P30CFG, GPIO_MUX_GPIO);
	GPIO_ENABLE_OUTPUT(P3TRIS, GPIO_PIN_0);
	D_Phase = Off;
	
	//BAT_EN
	GPIO_SET_MUX_MODE(P31CFG, GPIO_MUX_GPIO);
	GPIO_ENABLE_OUTPUT(P3TRIS, GPIO_PIN_1);
	BAT_EN = On;
	
	//LEDG
	GPIO_SET_MUX_MODE(P23CFG, GPIO_MUX_GPIO);
	GPIO_ENABLE_OUTPUT(P2TRIS, GPIO_PIN_3);
	LEDG = Off;
	
	//LEDR
	GPIO_SET_MUX_MODE(P22CFG, GPIO_MUX_GPIO);
	GPIO_ENABLE_OUTPUT(P2TRIS, GPIO_PIN_2);
	LEDR = Off;
	
	
	//K3 K0 K1
	
	GPIO_SET_MUX_MODE(P21CFG, GPIO_MUX_GPIO);		// 
	GPIO_ENABLE_INPUT(P2TRIS, GPIO_PIN_1);			// 
	GPIO_ENABLE_UP(P2UP, GPIO_PIN_1);				// 
	
	
	GPIO_SET_MUX_MODE(P17CFG, GPIO_MUX_GPIO);		//设置P017为GPIO模式
	GPIO_ENABLE_INPUT(P1TRIS, GPIO_PIN_7);			//设置为输入模式
	GPIO_ENABLE_UP(P1UP, GPIO_PIN_7);				//开启上拉

	//  
	GPIO_SET_MUX_MODE(P16CFG, GPIO_MUX_GPIO);		//设置P16为GPIO模式
	GPIO_ENABLE_INPUT(P1TRIS, GPIO_PIN_6);			//设置为输入模式
	GPIO_ENABLE_UP(P1UP, GPIO_PIN_6);				//开启上拉

//CHG_FULL
	GPIO_SET_MUX_MODE(P15CFG, GPIO_MUX_GPIO);		//设置P15为GPIO模式
	GPIO_ENABLE_INPUT(P1TRIS, GPIO_PIN_5);			//设置为输入模式
	GPIO_ENABLE_UP(P1UP, GPIO_PIN_5);				//开启上拉

//CHG_CHARGE
	GPIO_SET_MUX_MODE(P14CFG, GPIO_MUX_GPIO);		//设置P14为GPIO模式
	GPIO_ENABLE_INPUT(P1TRIS, GPIO_PIN_4);			//设置为输入模式
	GPIO_ENABLE_UP(P1UP, GPIO_PIN_4);				//开启上拉



	
	/*
	(2)设置中断方式
	*/
////	GPIO_SET_INT_MODE(P23EICFG, GPIO_INT_FALLING);	//设置为下降沿中断模式
////	GPIO_EnableInt(GPIO2, GPIO_PIN_3_MSK);			//开启P23中断
	
	
	/*
	(3)设置中断优先级
	*/
////	IRQ_SET_PRIORITY(IRQ_P2, IRQ_PRIORITY_LOW);
	/*
	(4)开启总中断
	*/	
	IRQ_ALL_ENABLE();

	
}
























