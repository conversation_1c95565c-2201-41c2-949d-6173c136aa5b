# 自转模式充电功能测试

## 功能需求
进入自转模式后，只要插上充电器开始充电就绿灯闪烁红灯灭，无论电机是否正在运行。充电时自转各个功能可以正常使用。充满时绿灯长亮红灯灭。只要拔下充电器灯光就要以当前状态动作。

## 实现方案

### 1. LED控制逻辑重构
将自转模式的LED控制集中到`LED_Control()`函数中，实现统一管理：

```c
void LED_Control(void)
{
    // 自转模式下的特殊LED控制
    if(System_Mode_Data == System_AutoRotate)
    {
        // 自转模式下的充电LED处理
        if(Charg_State_Buff)  // 充电状态
        {
            // 充电时：绿灯闪烁，红灯灭
            LEDROFF;  // 红灯灭
            if(CHG_CHARGE == 0)  // 正在充电
            {
                if(ledonoff) LEDGON; 
                else LEDGOFF;
            }
            else  // 充满电
            {
                LEDGON;  // 绿灯长亮
            }
        }
        else  // 未充电状态
        {
            // 未充电时：根据自转状态显示
            LEDGOFF;  // 绿灯灭
            if(auto_rotate_running)
            {
                // 自转运行时：红灯闪烁
                if(auto_rotate_flash)
                {
                    LEDRON;  // 红灯亮
                }
                else
                {
                    LEDROFF; // 红灯灭
                }
            }
            else
            {
                // 自转待机时：红灯长亮
                LEDRON;
            }
        }
    }
    // ... 非自转模式的原有逻辑
}
```

### 2. 充电状态检测
- `Charg_State_Buff`: 充电连接状态标志
- `CHG_CHARGE`: 硬件充电状态检测引脚 (P14)
  - 0 = 正在充电
  - 1 = 充电完成或未充电
- `CHG_FULL`: 硬件充满状态检测引脚 (P15)

### 3. LED状态移除
移除了自转模式下的直接LED控制，改为由`LED_Control()`函数统一管理：
- 移除进入自转模式时的`LEDGOFF; LEDRON;`
- 移除按键处理中的`LEDRON;`
- 移除电机停止时的`LEDRON;`

## 测试场景

### 场景1：自转待机状态充电
1. 进入自转模式（红灯长亮）
2. 插入充电器 → 绿灯闪烁，红灯灭 ✅
3. 充电完成 → 绿灯长亮，红灯灭 ✅
4. 拔出充电器 → 恢复红灯长亮 ✅

### 场景2：自转运行状态充电
1. 进入自转模式，按K1开始顺时针自转（红灯闪烁）
2. 插入充电器 → 绿灯闪烁，红灯灭 ✅
3. 充电完成 → 绿灯长亮，红灯灭 ✅
4. 拔出充电器 → 恢复红灯闪烁（继续自转） ✅

### 场景3：充电时功能操作
1. 自转模式下插入充电器（绿灯闪烁）
2. 按K1开始顺时针自转 → 电机正常运行，绿灯继续闪烁 ✅
3. 按K3切换到逆时针自转 → 电机正常运行，绿灯继续闪烁 ✅
4. 再按K3停止自转 → 电机停止，绿灯继续闪烁 ✅
5. K2长按退出自转模式 → 正常退出 ✅

### 场景4：充电状态变化
1. 自转模式下插入充电器开始充电（绿灯闪烁）
2. 充电完成 → 绿灯变为长亮 ✅
3. 拔出充电器 → 根据当前自转状态显示LED ✅

## LED状态总结

### 自转模式下的LED状态表
| 充电状态 | 自转状态 | 红灯 | 绿灯 |
|---------|---------|------|------|
| 未充电   | 待机     | 长亮 | 灭   |
| 未充电   | 运行     | 闪烁 | 灭   |
| 充电中   | 任意     | 灭   | 闪烁 |
| 充满电   | 任意     | 灭   | 长亮 |

### 关键特性
1. **充电优先级最高**：一旦检测到充电状态，LED立即切换到充电显示模式
2. **功能不受影响**：充电时所有自转功能（K1顺时针、K3逆时针、K2退出）正常工作
3. **状态恢复**：拔出充电器后立即恢复到当前自转状态对应的LED显示
4. **实时响应**：充电状态变化（插入/拔出/充满）立即反映在LED上

## 代码修改要点

### 1. 统一LED管理
- 所有LED控制集中到`LED_Control()`函数
- 移除分散在各处的直接LED操作

### 2. 充电状态检测
- 利用现有的`Charg_State_Buff`和`CHG_CHARGE`信号
- 在自转模式下添加特殊的充电LED处理逻辑

### 3. 状态优先级
- 充电状态 > 自转运行状态 > 自转待机状态
- 确保充电时LED显示正确，拔出后恢复原状态

这个实现确保了自转模式下充电功能的完整性和用户体验的一致性。
